'use client'

import React, { useState, useEffect } from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Plus, Search, Grid3X3, List, Eye, Edit2, Trash2, Package, AlertTriangle, X, Check, Calendar, User, ChevronRight } from 'lucide-react'
import { ItemStatus, Item, Category, Location, ItemFormData, BorrowingFormData } from '@/types'

// Enhanced status system with visual indicators
const getStatusConfig = (status: ItemStatus, isLoaned = false) => {
  if (isLoaned) {
    return {
      label: 'Dipinjam',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      dotColor: 'bg-yellow-400',
      bgColor: '#facc15'
    }
  }

  switch (status) {
    case ItemStatus.AVAILABLE:
      return {
        label: 'Tersedia',
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-400',
        bgColor: '#22c55e'
      }
    case ItemStatus.OUT_OF_STOCK:
      return {
        label: 'Perbaikan',
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-400',
        bgColor: '#ef4444'
      }
    case ItemStatus.DISCONTINUED:
      return {
        label: 'Pensiun',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
    default:
      return {
        label: 'Unknown',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
  }
}

// View modes
type ViewMode = 'table' | 'card'

// Sidebar panel types
type SidebarPanel = 'none' | 'add-item' | 'item-detail' | 'borrowing'

interface SidebarState {
  isOpen: boolean
  panel: SidebarPanel
  data?: any
}

const InventoryPage: React.FC = () => {
  // Existing states
  const [items, setItems] = useState<Item[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)

  // New states for enhanced functionality
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [sidebar, setSidebar] = useState<SidebarState>({ isOpen: false, panel: 'none' })
  const [editingField, setEditingField] = useState<{ itemId: string, field: string } | null>(null)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  // Form data states
  const [formData, setFormData] = useState<ItemFormData>({
    name: '',
    description: '',
    stock: 0,
    minStock: 5,
    categoryId: '',
    locationId: ''
  })

  const [borrowingData, setBorrowingData] = useState<BorrowingFormData>({
    borrowerName: '',
    purpose: '',
    expectedReturnDate: '',
    notes: '',
    items: []
  })

  useEffect(() => {
    fetchItems()
    fetchCategories()
    fetchLocations()
  }, [])

  const fetchItems = async () => {
    try {
      const response = await fetch('/api/items')
      if (response.ok) {
        const data = await response.json()
        setItems((data.data as Item[]) || [])
      }
    } catch (error) {
      console.error('Error fetching items:', error)
    } finally {
      setIsLoadingData(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories((data.data as Category[]) || [])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchLocations = async () => {
    try {
      const response = await fetch('/api/locations')
      if (response.ok) {
        const data = await response.json()
        setLocations((data.data as Location[]) || [])
      }
    } catch (error) {
      console.error('Error fetching locations:', error)
    }
  }

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.location?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Multi-select handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(new Set(filteredItems.map(item => item.id)))
    } else {
      setSelectedItems(new Set())
    }
  }

  const handleSelectItem = (itemId: string, checked: boolean) => {
    const newSelected = new Set(selectedItems)
    if (checked) {
      newSelected.add(itemId)
    } else {
      newSelected.delete(itemId)
    }
    setSelectedItems(newSelected)
  }

  // Sidebar handlers
  const openSidebar = (panel: SidebarPanel, data?: any) => {
    setSidebar({ isOpen: true, panel, data })
  }

  const closeSidebar = () => {
    setSidebar({ isOpen: false, panel: 'none', data: null })
    setFormData({
      name: '',
      description: '',
      stock: 0,
      minStock: 5,
      categoryId: '',
      locationId: ''
    })
  }

  // Get smart actions based on selected items
  const getSmartActions = () => {
    const selectedItemsData = items.filter(item => selectedItems.has(item.id))
    const availableItems = selectedItemsData.filter(item => item.status === ItemStatus.AVAILABLE)

    if (selectedItems.size === 0) return []

    const actions = []

    if (selectedItems.size === 1) {
      const item = selectedItemsData[0]
      const isLoaned = item.borrowingItems && item.borrowingItems.some((bi: any) => bi.status === 'ACTIVE')

      if (item.status === ItemStatus.AVAILABLE && !isLoaned) {
        actions.push({ label: 'Pinjamkan', action: 'loan', variant: 'primary' as const })
        actions.push({ label: 'Atur Perbaikan', action: 'maintenance', variant: 'secondary' as const })
      } else if (isLoaned) {
        actions.push({ label: 'Tandai Kembali', action: 'return', variant: 'primary' as const })
        actions.push({ label: 'Perpanjang', action: 'extend', variant: 'secondary' as const })
      }
    } else {
      if (availableItems.length === selectedItems.size) {
        actions.push({ label: 'Pinjamkan Item Terpilih', action: 'multi-loan', variant: 'primary' as const })
      }
      actions.push({ label: 'Edit Massal', action: 'bulk-edit', variant: 'secondary' as const })
    }

    actions.push({ label: 'Hapus', action: 'delete', variant: 'danger' as const })
    return actions
  }

  // Action handlers
  const handleAction = (action: string) => {
    const selectedItemsData = items.filter(item => selectedItems.has(item.id))

    switch (action) {
      case 'loan':
      case 'multi-loan':
        setBorrowingData({
          borrowerName: '',
          purpose: '',
          expectedReturnDate: '',
          notes: '',
          items: selectedItemsData.map(item => ({
            itemId: item.id,
            quantity: 1,
            notes: ''
          }))
        })
        openSidebar('borrowing', { items: selectedItemsData, type: 'loan' })
        break
      case 'return':
        // Handle return logic
        break
      case 'extend':
        // Handle extend logic
        break
      case 'maintenance':
        // Handle maintenance logic
        break
      case 'bulk-edit':
        // Handle bulk edit logic
        break
      case 'delete':
        // Handle delete logic
        break
    }
  }

  // Table View Component
  const TableView: React.FC<{
    items: Item[]
    selectedItems: Set<string>
    onSelectAll: (checked: boolean) => void
    onSelectItem: (itemId: string, checked: boolean) => void
    onOpenDetail: (item: Item) => void
    editingField: { itemId: string, field: string } | null
    setEditingField: (field: { itemId: string, field: string } | null) => void
    hoveredItem: string | null
    setHoveredItem: (itemId: string | null) => void
    categories: Category[]
    onUpdateItem: () => void
  }> = ({
    items, selectedItems, onSelectAll, onSelectItem, onOpenDetail,
    editingField, setEditingField, hoveredItem, setHoveredItem,
    categories, onUpdateItem
  }) => {
    const allSelected = items.length > 0 && items.every(item => selectedItems.has(item.id))
    const someSelected = items.some(item => selectedItems.has(item.id))

    const handleInlineEdit = async (itemId: string, field: string, value: string) => {
      try {
        const response = await fetch(`/api/items/${itemId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ [field]: value })
        })
        if (response.ok) {
          onUpdateItem()
        }
      } catch (error) {
        console.error('Error updating item:', error)
      }
      setEditingField(null)
    }

    return (
      <Card className="glass">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="w-12 px-4 py-3">
                    <input
                      type="checkbox"
                      checked={allSelected}
                      ref={(el) => {
                        if (el) el.indeterminate = someSelected && !allSelected
                      }}
                      onChange={(e) => onSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Nama Item</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Kategori</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => {
                  const statusConfig = getStatusConfig(item.status,
                    item.borrowingItems && item.borrowingItems.some((bi: any) => bi.status === 'ACTIVE')
                  )

                  return (
                    <tr
                      key={item.id}
                      className="border-b border-gray-100 hover:bg-gray-50 transition-colors relative"
                      onMouseEnter={() => setHoveredItem(item.id)}
                      onMouseLeave={() => setHoveredItem(null)}
                    >
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedItems.has(item.id)}
                          onChange={(e) => onSelectItem(item.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Package className="h-5 w-5 text-gray-500" />
                          </div>
                          <div>
                            {editingField?.itemId === item.id && editingField?.field === 'name' ? (
                              <input
                                type="text"
                                defaultValue={item.name}
                                autoFocus
                                onBlur={(e) => handleInlineEdit(item.id, 'name', e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    handleInlineEdit(item.id, 'name', e.currentTarget.value)
                                  }
                                  if (e.key === 'Escape') {
                                    setEditingField(null)
                                  }
                                }}
                                className="font-medium text-gray-900 bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            ) : (
                              <div
                                className="font-medium text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                                onClick={() => setEditingField({ itemId: item.id, field: 'name' })}
                              >
                                {item.name}
                              </div>
                            )}
                            <div className="text-sm text-gray-500">{item.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {editingField?.itemId === item.id && editingField?.field === 'category' ? (
                          <select
                            defaultValue={item.categoryId}
                            autoFocus
                            onBlur={(e) => handleInlineEdit(item.id, 'categoryId', e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleInlineEdit(item.id, 'categoryId', e.currentTarget.value)
                              }
                              if (e.key === 'Escape') {
                                setEditingField(null)
                              }
                            }}
                            className="bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            {categories.map(cat => (
                              <option key={cat.id} value={cat.id}>{cat.name}</option>
                            ))}
                          </select>
                        ) : (
                          <span
                            className="cursor-pointer hover:text-blue-600 transition-colors"
                            onClick={() => setEditingField({ itemId: item.id, field: 'category' })}
                          >
                            {item.category?.name}
                          </span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${statusConfig.color}`}>
                            {statusConfig.label}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onOpenDetail(item)}
                          className="flex items-center space-x-1"
                        >
                          <Eye className="h-4 w-4" />
                          <span>Detail</span>
                        </Button>
                      </td>

                      {/* Quick Preview on Hover */}
                      {hoveredItem === item.id && (
                        <div className="absolute left-full top-0 ml-2 z-10 bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-64">
                          <div className="flex items-center space-x-3">
                            <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                              <Package className="h-8 w-8 text-gray-500" />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900">{item.name}</h4>
                              <p className="text-sm text-gray-500">{item.category?.name}</p>
                              <div className="flex items-center space-x-2 mt-1">
                                <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
                                <span className="text-xs text-gray-600">{statusConfig.label}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <AppLayout>
      <div className="flex h-screen overflow-hidden">
        {/* Main Content */}
        <div className={`flex-1 flex flex-col transition-all duration-300 ${sidebar.isOpen ? 'mr-96' : ''}`}>
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-white">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Manajemen Inventaris</h1>
              <p className="text-gray-600 mt-1">
                Kelola inventaris barang dengan sistem yang terintegrasi
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <Button
                  variant={viewMode === 'table' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                  className="px-3 py-2"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'card' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('card')}
                  className="px-3 py-2"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>

              <Button
                onClick={() => openSidebar('add-item')}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Tambah Item</span>
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="p-6 border-b border-gray-200 bg-white">
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="Cari barang, kategori, atau lokasi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="h-4 w-4" />}
                />
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-auto p-6">
            {isLoadingData ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Memuat data...</span>
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada barang</h3>
                <p className="text-gray-600 mb-4">Mulai dengan menambahkan barang pertama</p>
                <Button onClick={() => openSidebar('add-item')} className="flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Tambah Barang</span>
                </Button>
              </div>
            ) : (
              <>
                {viewMode === 'table' ? (
                  <TableView
                    items={filteredItems}
                    selectedItems={selectedItems}
                    onSelectAll={handleSelectAll}
                    onSelectItem={handleSelectItem}
                    onOpenDetail={(item) => openSidebar('item-detail', item)}
                    editingField={editingField}
                    setEditingField={setEditingField}
                    hoveredItem={hoveredItem}
                    setHoveredItem={setHoveredItem}
                    categories={categories}
                    onUpdateItem={fetchItems}
                  />
                ) : (
                  <div className="text-center py-12">
                    <Grid3X3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Card view will be implemented next</p>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Floating Action Panel */}
          {selectedItems.size > 0 && (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
              <Card className="glass shadow-2xl border-gray-300">
                <CardContent className="px-6 py-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">{selectedItems.size}</span>
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        {selectedItems.size} item terpilih
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      {getSmartActions().map((action, index) => (
                        <Button
                          key={index}
                          variant={action.variant}
                          size="sm"
                          onClick={() => handleAction(action.action)}
                        >
                          {action.label}
                        </Button>
                      ))}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedItems(new Set())}
                        className="p-2"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Contextual Sidebar - Placeholder */}
        {sidebar.isOpen && (
          <div className="fixed right-0 top-0 h-full w-96 bg-white border-l border-gray-200 shadow-xl z-40 transform transition-transform duration-300">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {sidebar.panel === 'add-item' && 'Tambah Item Baru'}
                {sidebar.panel === 'item-detail' && 'Detail Item'}
                {sidebar.panel === 'borrowing' && 'Pinjam Barang'}
              </h2>
              <Button variant="ghost" size="sm" onClick={closeSidebar} className="p-2">
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-6">
              <p className="text-gray-600">Sidebar content for {sidebar.panel} will be implemented next</p>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}

export default InventoryPage
